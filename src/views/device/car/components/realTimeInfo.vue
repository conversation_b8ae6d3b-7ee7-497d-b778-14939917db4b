<template>
    <div class="pl-3">
        <div class="ul-box flex justify-between mb-5">
            <ul class="flex items-center flex-1 w-0 title-family px-2.5">
                <!-- <li class="mr-4" style=""> -->
                <div class="text-sm text-secondar-text dark:text-60-dark">
                    {{ $t('Device No') }}：{{ data.sn || '-' }}

                    <span class="descriptions-item-label ml-4">
                        <!-- {{ $t('运行状态') }}： -->
                    </span>
                </div>
                <div class="text-sm text-secondar-text dark:text-60-dark">
                    {{ $t('Device Status') }}：{{
                        $t(getState(data.status).label) || '-'
                    }}
                </div>
                <!-- </li> -->
            </ul>
            <div class="mr-1.5 flex items-center">
                <!-- <el-button plain round @click="lookBattery" linear disabled>{{
                    $t('Relay Status')
                }}</el-button> -->
            </div>
        </div>
        <div :style="objectStyle" class="flex flex-wrap place-content-start">
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('SOC') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.soc || data?.soc == 0 ? data?.soc + '%' : '0' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_pingjunwendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.tempAvg || data.tempAvg == 0
                            ? data.tempAvg + ' °C'
                            : '0'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('signal_strength') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.signal4g || '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('SOH') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.soh || data.soh == 0 ? data.soh + '%' : '0' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_zuigaowendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.tempMax || data.tempMax == 0
                            ? data.tempMax + ' °C'
                            : '0'
                    }}{{ data?.tempMaxId ? ' | #' + data.tempMaxId : '' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Location') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.Location || '-' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_zongdianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.sysVoltage || data.sysVoltage == 0
                            ? data.sysVoltage + ' V'
                            : '0'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_zuidiwendu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.tempMin || data?.tempMin == 0
                            ? data?.tempMin + ' °C'
                            : '0'
                    }}{{ data?.tempMinId ? ' | #' + data?.tempMinId : '' }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Cell Series Count') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.cellNub || '0' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_zongdianliu') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.sysCurrent || data.sysCurrent == 0
                            ? data.sysCurrent.toFixed(1) + ' A'
                            : '0'
                    }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_pingjundianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.volAvg || data.volAvg === 0
                            ? data.volAvg + ' V'
                            : '0'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Cell temperature sensor count') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.tempNub || '0' }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Positive insulation resistance') }}：
                </div>
                <div class="descriptions-content">
                    {{ (data.insZ || 0) + ' kΩ' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_zuigaodianya') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data?.volMax || data.volMax == 0
                            ? data.volMax + ' V'
                            : '0'
                    }}
                    {{ data?.volMaxId ? ' | #' + data?.volMaxId : '' }}
                </div>
            </div>
            <div class="descriptions-item">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('System time') }}：
                </div>
                <div class="descriptions-content">
                    {{
                        data.time
                            ? dayjs(data.time * 1000).format(
                                  'YYYY/MM/DD HH:mm:ss'
                              )
                            : '-'
                    }}
                </div>
            </div>

            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('Negative insulation resistance') }}：
                </div>
                <div class="descriptions-content">
                    {{ (data.insF || 0) + ' kΩ' }}
                </div>
            </div>
            <div class="descriptions-item mb-5">
                <div :class="locale" class="descriptions-item-label">
                    {{ $t('device_zuididianya') }}：
                </div>
                <div class="descriptions-content">
                    {{ data?.volMin || data.volMin ? data.volMin + ' V' : '0' }}
                    {{ data?.volMinId ? ' | #' + data?.volMinId : '' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { accountUnit, batteryStatus } from '../../const'
import { computed, onMounted, ref } from 'vue'
import store from '@/store'
import dayjs from 'dayjs'
import { useI18n } from 'vue-i18n'
import Decimal from 'decimal.js'
import { chargeStateP } from '@/common/util.js'
export default {
    name: 'bmsBox',
    props: {
        objectStyle: {
            type: Object,
            default: () => {
                return {
                    height: '100%',
                }
            },
        },
        data: {
            type: Object,
            default: () => {
                return {}
            },
        },
        deviceData: {
            type: Object,
            default: () => {
                return {}
            },
        },
    },
    setup(props, { emit }) {
        const { t, locale } = useI18n()
        const lookBattery = () => {
            emit('lookBattery', true)
        }

        onMounted(async () => {})
        const getState = (status) => {
            if (status !== null && status !== undefined && status !== '') {
                const item = chargeStateP.find((s) => s.value == status)
                return item
            } else {
                return {
                    label: '',
                }
            }
        }
        return {
            accountUnit,
            batteryStatus,
            lookBattery,
            getState,
            dayjs,
            Decimal,
            t,
            locale,
        }
    },
}
</script>

<style scoped lang="less">
.descriptions-item {
    width: 33.33%;
    display: flex;

    font-size: 14px;

    .descriptions-item-label {
        color: var(--text-60);
        padding-left: 22px;
        max-width: 70%;
        // width: 50%;
        &.en {
            padding-left: 12px;
        }
    }

    .descriptions-content {
        color: var(--text-100);
        flex: 1;
        width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 0;
    }

    &:nth-child(3n-2) {
        .descriptions-item-label {
            padding-left: 9px;
        }
    }
}

.origin {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: rgba(51, 190, 79, 1);
    vertical-align: middle;
    border-radius: 50%;
    &.origin-gray {
        background-color: rgba(34, 34, 34, 0.5);
    }
}

.ul-box {
    // height: 40px;
    padding: 10px 0;
    // line-height: 40px;
    line-height: 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 16px;
    // background: var(--bg-f5);
}
</style>
